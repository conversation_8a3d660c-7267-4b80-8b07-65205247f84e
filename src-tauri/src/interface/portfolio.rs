use std::sync::Arc;

use serde::{Deserialize, Serialize};
use tauri::{AppHandle, Runtime};

use crate::{
    data_source::risk_free_rate::get_risk_free_rate,
    efficient_frontier::portfolio::{EfficientFrontierDot, Portfolio},
    indicators::calculations::ProfitRateCalculationType,
    interface::app_utils::{error, info},
};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PortfolioOptimizationRequest {
    pub stock_codes: Vec<String>,
    pub window_days: u64,
    pub max_iter: usize,
    pub profit_type: Option<ProfitRateCalculationType>,
    pub l2: f64,
    pub covariance_lambda: f64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PortfolioOptimizationResult {
    pub asset_weights: Vec<AssetWeight>,
    pub expected_return: f64,
    pub standard_deviation: f64,
    pub optimization_type: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AssetWeight {
    pub code: String,
    pub weight: f64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MinVarianceGivenReturnRequest {
    pub stock_codes: Vec<String>,
    pub window_days: u64,
    pub target_return: f64,
    pub max_iter: usize,
    pub profit_type: Option<ProfitRateCalculationType>,
    pub l2: f64,
    pub covariance_lambda: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaxReturnGivenVarianceRequest {
    pub stock_codes: Vec<String>,
    pub window_days: u64,
    pub target_variance: f64,
    pub max_iter: usize,
    pub profit_type: Option<ProfitRateCalculationType>,
    pub l2: f64,
    pub covariance_lambda: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DrawEfficientFrontierForPortfolioRequest {
    codes: Vec<Arc<str>>,
    window_days: u64,
    n_dots: usize,
    max_iter: usize,
    l2: f64,
    covariance_lambda: f64,
    profit_type: ProfitRateCalculationType,
}

async fn create_portfolio_from_codes(
    stock_codes: &[String],
    window_days: u64,
    profit_type: ProfitRateCalculationType,
) -> anyhow::Result<Portfolio> {
    let codes: Vec<Arc<str>> = stock_codes.iter().map(|s| Arc::from(s.as_str())).collect();

    let mut portfolio = Portfolio::new();
    portfolio.set_profit_type(profit_type);
    portfolio.add_stock_from_codes(&codes, window_days).await?;

    Ok(portfolio)
}

fn portfolio_to_result(
    mut portfolio: Portfolio,
    optimization_type: String,
) -> anyhow::Result<PortfolioOptimizationResult> {
    let asset_weights = portfolio
        .get_asset_weights()
        .into_iter()
        .map(|(code, weight)| AssetWeight {
            code: code.to_string(),
            weight,
        })
        .collect();

    let expected_return = portfolio.expected_return();
    let standard_deviation = portfolio.standard_deviation()?;

    Ok(PortfolioOptimizationResult {
        asset_weights,
        expected_return,
        standard_deviation,
        optimization_type,
    })
}

/// 最小方差组合优化（不考虑收益约束）
#[tauri::command]
pub async fn optimize_min_variance<R: Runtime>(
    app: AppHandle<R>,
    request: PortfolioOptimizationRequest,
) -> Result<PortfolioOptimizationResult, String> {
    info!(
        app,
        serde_json::to_string_pretty(&request).map_err(|e| e.to_string())?
    )?;
    info!(app, "开始最小方差组合优化...")?;

    let profit_type = request
        .profit_type
        .unwrap_or(ProfitRateCalculationType::Logarithm);

    let mut portfolio =
        create_portfolio_from_codes(&request.stock_codes, request.window_days, profit_type)
            .await
            .map_err(|e| {
                let err_msg = format!("创建投资组合失败: {}", e);
                let _ = error!(app, &err_msg);
                err_msg
            })?;

    portfolio.set_l2(request.l2);
    portfolio.set_covariance_lambda(request.covariance_lambda);

    portfolio
        .optimize_min_variance(request.max_iter)
        .map_err(|e| {
            let err_msg = format!("最小方差优化失败: {}", e);
            let _ = error!(app, &err_msg);
            err_msg
        })?;

    let result = portfolio_to_result(portfolio, "MinVariance".to_string()).map_err(|e| {
        let err_msg = format!("生成结果失败: {}", e);
        let _ = error!(app, &err_msg);
        err_msg
    })?;

    info!(app, "最小方差组合优化完成")?;
    Ok(result)
}

/// 给定收益约束下的最小方差组合优化
#[tauri::command]
pub async fn optimize_min_variance_given_return<R: Runtime>(
    app: AppHandle<R>,
    request: MinVarianceGivenReturnRequest,
) -> Result<PortfolioOptimizationResult, String> {
    info!(
        app,
        serde_json::to_string_pretty(&request).map_err(|e| e.to_string())?
    )?;
    info!(app, "开始给定收益下的最小方差组合优化...")?;

    let profit_type = request
        .profit_type
        .unwrap_or(ProfitRateCalculationType::Logarithm);

    let mut portfolio =
        create_portfolio_from_codes(&request.stock_codes, request.window_days, profit_type)
            .await
            .map_err(|e| {
                let err_msg = format!("创建投资组合失败: {}", e);
                let _ = error!(app, &err_msg);
                err_msg
            })?;

    portfolio.set_l2(request.l2);
    portfolio.set_covariance_lambda(request.covariance_lambda);

    portfolio
        .optimize_min_variance_given_return(request.target_return, request.max_iter)
        .map_err(|e| {
            let err_msg = format!("给定收益下最小方差优化失败: {}", e);
            let _ = error!(app, &err_msg);
            err_msg
        })?;

    let result =
        portfolio_to_result(portfolio, "MinVarianceGivenReturn".to_string()).map_err(|e| {
            let err_msg = format!("生成结果失败: {}", e);
            let _ = error!(app, &err_msg);
            err_msg
        })?;

    info!(app, "给定收益下的最小方差组合优化完成")?;
    Ok(result)
}

/// 给定方差约束下的最大收益组合优化
#[tauri::command]
pub async fn optimize_max_return_given_variance<R: Runtime>(
    app: AppHandle<R>,
    request: MaxReturnGivenVarianceRequest,
) -> Result<PortfolioOptimizationResult, String> {
    info!(
        app,
        serde_json::to_string_pretty(&request).map_err(|e| e.to_string())?
    )?;
    info!(app, "开始给定方差下的最大收益组合优化...")?;

    let profit_type = request
        .profit_type
        .unwrap_or(ProfitRateCalculationType::Logarithm);

    let mut portfolio =
        create_portfolio_from_codes(&request.stock_codes, request.window_days, profit_type)
            .await
            .map_err(|e| {
                let err_msg = format!("创建投资组合失败: {}", e);
                let _ = error!(app, &err_msg);
                err_msg
            })?;

    portfolio.set_l2(request.l2);
    portfolio.set_covariance_lambda(request.covariance_lambda);
    portfolio
        .optimize_max_return_given_variance(request.target_variance, request.max_iter)
        .map_err(|e| {
            let err_msg = format!("给定方差下最大收益优化失败: {}", e);
            let _ = error!(app, &err_msg);
            err_msg
        })?;

    let result =
        portfolio_to_result(portfolio, "MaxReturnGivenVariance".to_string()).map_err(|e| {
            let err_msg = format!("生成结果失败: {}", e);
            let _ = error!(app, &err_msg);
            err_msg
        })?;

    info!(app, "给定方差下的最大收益组合优化完成")?;
    Ok(result)
}

#[tauri::command]
pub async fn optimize_max_sharp_ratio<R: Runtime>(
    app: AppHandle<R>,
    request: MaxReturnGivenVarianceRequest,
) -> Result<PortfolioOptimizationResult, String> {
    info!(
        app,
        serde_json::to_string_pretty(&request).map_err(|e| e.to_string())?
    )?;
    info!(app, "最大化夏普率组合优化...")?;

    let profit_type = request
        .profit_type
        .unwrap_or(ProfitRateCalculationType::Logarithm);

    let mut portfolio =
        create_portfolio_from_codes(&request.stock_codes, request.window_days, profit_type)
            .await
            .map_err(|e| {
                let err_msg = format!("创建投资组合失败: {}", e);
                let _ = error!(app, &err_msg);
                err_msg
            })?;

    let rf = get_risk_free_rate().await.map_err(|e| e.to_string())?;
    portfolio.set_l2(request.l2);
    portfolio.set_covariance_lambda(request.covariance_lambda);
    portfolio
        .optimize_max_sharpe(rf, request.max_iter)
        .map_err(|e| {
            let err_msg = format!("最大化夏普率优化失败: {}", e);
            let _ = error!(app, &err_msg);
            err_msg
        })?;

    let result = portfolio_to_result(portfolio, "MaxSharpRatio".to_string()).map_err(|e| {
        let err_msg = format!("生成结果失败: {}", e);
        let _ = error!(app, &err_msg);
        err_msg
    })?;

    info!(app, "最大化夏普率优化失败组合优化完成")?;
    Ok(result)
}

#[tauri::command]
pub async fn optimize_risk_parity<R: Runtime>(
    app: AppHandle<R>,
    request: MaxReturnGivenVarianceRequest,
) -> Result<PortfolioOptimizationResult, String> {
    info!(
        app,
        serde_json::to_string_pretty(&request).map_err(|e| e.to_string())?
    )?;
    info!(app, "风险平价组合优化...")?;

    let profit_type = request
        .profit_type
        .unwrap_or(ProfitRateCalculationType::Logarithm);

    let mut portfolio =
        create_portfolio_from_codes(&request.stock_codes, request.window_days, profit_type)
            .await
            .map_err(|e| {
                let err_msg = format!("创建投资组合失败: {}", e);
                let _ = error!(app, &err_msg);
                err_msg
            })?;

    portfolio.set_l2(request.l2);
    portfolio.set_covariance_lambda(request.covariance_lambda);

    portfolio
        .optimize_risk_parity(request.max_iter)
        .map_err(|e| {
            let err_msg = format!("风险平价组合优化失败: {}", e);
            let _ = error!(app, &err_msg);
            err_msg
        })?;

    let result =
        portfolio_to_result(portfolio, "RiskParitOptimization".to_string()).map_err(|e| {
            let err_msg = format!("生成结果失败: {}", e);
            let _ = error!(app, &err_msg);
            err_msg
        })?;

    info!(app, "风险平价组合优化完成")?;
    Ok(result)
}

#[tauri::command]
pub async fn draw_efficient_frontier<R: Runtime>(
    app: AppHandle<R>,
    request: DrawEfficientFrontierForPortfolioRequest,
) -> Result<Vec<EfficientFrontierDot>, String> {
    info!(app, "正在画图：有效前沿")?;

    let result = Portfolio::efficient_frontier(
        &request.codes,
        request.window_days,
        request.n_dots,
        request.max_iter,
        request.l2,
        request.covariance_lambda,
        request.profit_type,
    )
    .await
    .map_err(|e| e.to_string())?;

    Ok(result)
}

#[tokio::test]
async fn test_portfolio_optimization() {
    use crate::indicators::calculations::ProfitRateCalculationType;

    let request = PortfolioOptimizationRequest {
        stock_codes: vec!["002351".to_string(), "002670".to_string()],
        window_days: 365,
        max_iter: 1000,
        profit_type: Some(ProfitRateCalculationType::Logarithm),
        l2: 0.01,
        covariance_lambda: 0.1,
    };

    let mut portfolio = create_portfolio_from_codes(
        &request.stock_codes,
        request.window_days,
        request
            .profit_type
            .unwrap_or(ProfitRateCalculationType::Logarithm),
    )
    .await
    .expect("Failed to create portfolio");

    // 测试最小方差优化
    portfolio
        .optimize_min_variance(request.max_iter)
        .expect("Failed to optimize min variance");

    let result = portfolio_to_result(portfolio, "MinVariance".to_string())
        .expect("Failed to convert to result");

    println!("Test result: {:#?}", result);
    assert_eq!(result.asset_weights.len(), 2);
    assert!(!result.asset_weights.is_empty());
}
