use anyhow::{anyhow, bail, Context};
use chrono::{Days, Local, NaiveDate};
use hashbrown::HashSet;
use optimization_engine::{
    constraints::Simplex,
    panoc::{PANOCCache, PANOCOptimizer},
    Optimizer, Problem, SolverError,
};
use rayon::iter::{IndexedParallelIterator, IntoParallelIterator, ParallelIterator};
use serde::{Deserialize, Serialize};

use std::sync::Arc;

use derive_getters::Getters;

use crate::{
    data_source::adjusted_share_price::{get_adjusted_share_price, AdjustDirection, AssetPrice},
    efficient_frontier::correlation::{calculate_correlation_coefficient, covariance},
    indicators::calculations::{annualized_return, ProfitRateCalculationType},
};

#[derive(Debug, Clone, Copy, Deserialize, Serialize, Getters)]
pub struct EfficientFrontierDot {
    standard_deviation: f64,
    return_rate: f64,
}

#[derive(Debug, <PERSON><PERSON>, Get<PERSON>, Serialize, Deserialize)]
pub struct FinancialAsset {
    prices: Arc<[AssetPrice]>,
    std: f64,
    weight: f64,
    annualized_profit_rate: f64,
    code: Arc<str>,
}

impl FinancialAsset {
    fn new(
        prices: Arc<[AssetPrice]>,
        profit_type: ProfitRateCalculationType,
        code: Arc<str>,
    ) -> Option<Self> {
        let p = prices.iter().map(|x| *x.price()).collect::<Vec<f64>>();
        Some(Self {
            prices,
            std: -1.0,
            weight: -1.0,
            annualized_profit_rate: annualized_return(&p, profit_type)?,
            code: code.into(),
        })
    }

    fn get_prices_for_dates(&self, dates: &HashSet<NaiveDate>) -> Vec<f64> {
        let p = self
            .prices()
            .iter()
            .filter(|x| dates.contains(x.date()))
            .map(|x| *x.price())
            .collect::<Vec<f64>>();
        p
    }

    fn calculate_std(&mut self, dates: &HashSet<NaiveDate>) {
        if self.std != -1.0 {
            return;
        }
        let prices = self.get_prices_for_dates(dates);
        let n = prices.len() as f64;
        let mean = prices.iter().sum::<f64>() / n;
        self.std = (prices.iter().map(|x| (x - mean).powi(2)).sum::<f64>() / (n - 1.0)).sqrt();
    }
}

#[derive(Debug, Clone)]
pub struct Portfolio {
    financial_assets: Vec<FinancialAsset>,
    intersected_dates: HashSet<NaiveDate>,
    profit_type: ProfitRateCalculationType,
    asset_covariance_matrix: Option<Arc<[Vec<f64>]>>,
    l2: f64,
    covariance_lambda: f64,
}

impl Portfolio {
    pub fn new() -> Self {
        Self {
            financial_assets: vec![],
            intersected_dates: HashSet::new(),
            profit_type: ProfitRateCalculationType::Logarithm,
            asset_covariance_matrix: None,
            l2: 0.0,
            covariance_lambda: 0.0,
        }
    }

    pub fn set_l2(&mut self, l2: f64) {
        self.l2 = l2;
    }

    pub fn set_covariance_lambda(&mut self, lambda: f64) {
        self.covariance_lambda = lambda;
    }

    pub fn get_asset_covariance_matrix(&mut self) -> anyhow::Result<Arc<[Vec<f64>]>> {
        if let Some(m) = &self.asset_covariance_matrix {
            return Ok(m.clone());
        }

        let n = self.financial_assets.len();
        let assets = &self.financial_assets;
        let mut s = vec![vec![0.0; n]; n]; // 样本协方差矩阵
        let mut variances = vec![0.0; n];

        // 1. 计算样本协方差矩阵 S
        for i in 0..n {
            let arr_i = assets[i].get_prices_for_dates(&self.intersected_dates);
            for j in i..n {
                let arr_j = assets[j].get_prices_for_dates(&self.intersected_dates);
                let cov_ij = covariance(&arr_i, &arr_j)?;
                s[i][j] = cov_ij;
                s[j][i] = cov_ij;
            }
            variances[i] = s[i][i];
        }

        // 2. 构建目标矩阵 F（对角矩阵，只保留方差）
        let mut f = vec![vec![0.0; n]; n];
        for i in 0..n {
            f[i][i] = s[i][i];
        }

        // 3. 计算 Ledoit-Wolf 收缩系数 lambda
        // 简单实现：手动设定收缩强度 lambda = 0.1 ~ 0.5
        let lambda = self.covariance_lambda;

        let mut covar_matrix = vec![vec![0.0; n]; n];
        for i in 0..n {
            for j in 0..n {
                covar_matrix[i][j] = lambda * f[i][j] + (1.0 - lambda) * s[i][j];
            }
        }

        let result: Arc<[Vec<f64>]> = Arc::from(covar_matrix);
        self.asset_covariance_matrix.replace(result.clone());
        Ok(result)
    }

    pub fn get_correlation_coefficient_matrix(&mut self) -> anyhow::Result<Vec<Vec<f64>>> {
        for asset in self.financial_assets.iter_mut() {
            asset.calculate_std(&self.intersected_dates);
        }
        let covariance_matrix = self.get_asset_covariance_matrix()?;
        let assets = &self.financial_assets;
        let n = self.financial_assets.len();
        let mut matrix = vec![vec![0.0; n]; n];

        for i in 0..n {
            for j in 0..n {
                let asset_i_std = assets[i].std();
                let asset_j_std = assets[j].std();
                matrix[i][j] = covariance_matrix[i][j] / asset_i_std / asset_j_std;
            }
        }

        Ok(matrix)
    }

    pub fn set_profit_type(&mut self, profit_type: ProfitRateCalculationType) {
        self.profit_type = profit_type;
    }

    pub fn assets(&self) -> Vec<&FinancialAsset> {
        self.financial_assets.iter().collect()
    }

    pub fn get_asset_weights(&self) -> Vec<(Arc<str>, f64)> {
        self.financial_assets
            .iter()
            .map(|x| (x.code().clone(), *x.weight()))
            .collect()
    }

    pub fn add_stock(&mut self, code: Arc<str>, prices: Arc<[AssetPrice]>) -> anyhow::Result<()> {
        if self.intersected_dates.is_empty() {
            for price in prices.iter() {
                self.intersected_dates.insert(*price.date());
            }
        } else {
            let dates_set = HashSet::from_iter(prices.iter().map(|x| *x.date()));
            let intersected = self.intersected_dates.intersection(&dates_set).copied();
            self.intersected_dates = HashSet::from_iter(intersected);
        }

        let asset = FinancialAsset::new(prices, self.profit_type, code)
            .context("failed to create asset struct")?;
        self.financial_assets.push(asset);
        Ok(())
    }

    pub fn expected_return(&self) -> f64 {
        self.financial_assets
            .iter()
            .map(|x| x.weight() * x.annualized_profit_rate)
            .sum::<f64>()
    }

    pub fn standard_deviation(&mut self) -> anyhow::Result<f64> {
        for asset in self.financial_assets.iter_mut() {
            asset.calculate_std(&self.intersected_dates);
        }

        let mut var = 0.0;
        let length = self.financial_assets.len();
        for i in 0..length {
            let asset_i = self.financial_assets.get(i).unwrap();

            var += asset_i.weight().powi(2) * asset_i.std().powi(2);

            for j in (i + 1)..length {
                let asset_j = self.financial_assets.get(j).unwrap();

                let prices_i = asset_i.get_prices_for_dates(&self.intersected_dates);
                let prices_j = asset_j.get_prices_for_dates(&self.intersected_dates);

                let corr = calculate_correlation_coefficient(
                    &prices_i,
                    &prices_j,
                    *asset_i.std(),
                    *asset_j.std(),
                )?;

                var += 2.0
                    * asset_i.weight()
                    * asset_j.weight()
                    * asset_i.std()
                    * asset_j.std()
                    * corr;
            }
        }

        Ok(var.sqrt())
    }

    pub fn optimize_max_sharpe(
        &mut self,
        risk_free_rate: f64,
        max_iter: usize,
    ) -> anyhow::Result<()> {
        let n = self.financial_assets.len();
        if n == 0 {
            anyhow::bail!("No assets in portfolio");
        }

        let mu: Vec<f64> = self
            .financial_assets
            .iter()
            .map(|x| x.annualized_profit_rate)
            .collect();
        let sigma = self.get_asset_covariance_matrix()?;

        let f = |w: &[f64], cost: &mut f64| -> Result<(), SolverError> {
            let mean = mu.iter().zip(w).map(|(&m, &wi)| m * wi).sum::<f64>();
            let mut var = 0.0;
            for i in 0..n {
                for j in 0..n {
                    var += w[i] * w[j] * sigma[i][j];
                }
            }
            let std = var.sqrt().max(1e-12);
            *cost = -(mean - risk_free_rate) / std
                + self.l2 * w.iter().map(|wi| wi.powi(2)).sum::<f64>();
            Ok(())
        };

        let df = |w: &[f64], grad: &mut [f64]| -> Result<(), SolverError> {
            let mean = mu.iter().zip(w).map(|(&m, &wi)| m * wi).sum::<f64>();
            let mut sigma_w = vec![0.0; n];
            for i in 0..n {
                sigma_w[i] = sigma[i].iter().zip(w).map(|(&s, &wj)| s * wj).sum();
            }
            let var = w
                .iter()
                .zip(&sigma_w)
                .map(|(&wi, &sw)| wi * sw)
                .sum::<f64>();
            let sigma_val = var.sqrt().max(1e-12);
            let inv_sigma = 1.0 / sigma_val;
            let inv_sigma3 = 1.0 / (sigma_val * sigma_val * sigma_val);

            for i in 0..n {
                grad[i] = mu[i] * inv_sigma
                    - risk_free_rate * inv_sigma
                    - (mean - risk_free_rate) * inv_sigma3 * sigma_w[i];
                grad[i] = -grad[i]; // 因为 cost = -Sharpe
            }
            Ok(())
        };

        let constraints = Simplex::new(1.0);
        let problem = Problem::new(&constraints, df, f);
        let mut cache = PANOCCache::new(n, 1e-12, 10);
        let mut panoc = PANOCOptimizer::new(problem, &mut cache).with_max_iter(max_iter);

        // 初始权重均等
        let mut w = vec![1.0 / n as f64; n];
        panoc
            .solve(&mut w)
            .map_err(|e| anyhow!(format!("{e:#?}")))?;

        // 更新资产权重
        for (asset, &wi) in self.financial_assets.iter_mut().zip(&w) {
            asset.weight = wi;
        }

        Ok(())
    }

    /// 最小方差组合（不考虑收益）
    pub fn optimize_min_variance(&mut self, max_iter: usize) -> anyhow::Result<()> {
        let n = self.financial_assets.len();
        let sigma = self.get_asset_covariance_matrix()?;

        let f = |w: &[f64], cost: &mut f64| -> Result<(), SolverError> {
            let mut var = 0.0;
            for i in 0..n {
                for j in 0..n {
                    var += w[i] * w[j] * sigma[i][j];
                }
            }
            *cost = var + self.l2 * w.iter().map(|wi| wi.powi(2)).sum::<f64>();
            Ok(())
        };

        let df = |w: &[f64], grad: &mut [f64]| -> Result<(), SolverError> {
            for i in 0..n {
                grad[i] = 2.0 * sigma[i].iter().zip(w).map(|(&s, &wj)| s * wj).sum::<f64>();
            }
            Ok(())
        };

        let constraints = Simplex::new(1.0);
        let problem = Problem::new(&constraints, df, f);
        let mut cache = PANOCCache::new(n, 1e-12, 10);
        let mut panoc = PANOCOptimizer::new(problem, &mut cache).with_max_iter(max_iter);

        let mut w = vec![1.0 / n as f64; n];
        panoc
            .solve(&mut w)
            .map_err(|e| anyhow!(format!("{e:#?}")))?;

        for (asset, &wi) in self.financial_assets.iter_mut().zip(&w) {
            asset.weight = wi;
        }

        Ok(())
    }

    /// 给定方差求最大收益组合（penalty 方法）
    pub fn optimize_max_return_given_variance(
        &mut self,
        target_variance: f64,
        max_iter: usize,
    ) -> anyhow::Result<()> {
        let n = self.financial_assets.len();
        let mu: Arc<[f64]> = Arc::from(
            self.financial_assets
                .iter()
                .map(|x| x.annualized_profit_rate)
                .collect::<Vec<_>>(),
        );
        let sigma = self.get_asset_covariance_matrix()?;
        let penalty = 1e3; // 方差偏离惩罚系数
        let mv_closure = mu.clone();
        let signma_closure = sigma.clone();
        let l2_value = self.l2;
        let f = move |w: &[f64], cost: &mut f64| -> Result<(), SolverError> {
            let mean = mv_closure
                .iter()
                .zip(w)
                .map(|(&m, &wi)| m * wi)
                .sum::<f64>();
            let var = w
                .iter()
                .enumerate()
                .map(|(i, &wi)| {
                    w.iter()
                        .enumerate()
                        .map(|(j, &wj)| wi * wj * signma_closure[i][j])
                        .sum::<f64>()
                })
                .sum::<f64>();
            *cost = -mean
                + penalty * (var - target_variance).powi(2)
                + l2_value * w.iter().map(|wi| wi.powi(2)).sum::<f64>();
            Ok(())
        };

        let df = move |w: &[f64], grad: &mut [f64]| -> Result<(), SolverError> {
            let var = w
                .iter()
                .enumerate()
                .map(|(i, &wi)| {
                    w.iter()
                        .enumerate()
                        .map(|(j, &wj)| wi * wj * sigma[i][j])
                        .sum::<f64>()
                })
                .sum::<f64>();
            for i in 0..n {
                let grad_var_i = 2.0 * sigma[i].iter().zip(w).map(|(&s, &wj)| s * wj).sum::<f64>();
                grad[i] = -mu[i] + 2.0 * penalty * (var - target_variance) * grad_var_i;
            }
            Ok(())
        };

        let constraints = Simplex::new(1.0);
        let problem = Problem::new(&constraints, df, f);
        let mut cache = PANOCCache::new(n, 1e-12, 10);
        let mut panoc = PANOCOptimizer::new(problem, &mut cache).with_max_iter(max_iter);

        let mut w = vec![1.0 / n as f64; n];
        panoc
            .solve(&mut w)
            .map_err(|e| anyhow!(format!("{e:#?}")))?;

        for (asset, &wi) in self.financial_assets.iter_mut().zip(&w) {
            asset.weight = wi;
        }

        Ok(())
    }

    /// 给定收益求最小方差组合（penalty 方法）
    pub fn optimize_min_variance_given_return(
        &mut self,
        target_return: f64,
        max_iter: usize,
    ) -> anyhow::Result<()> {
        let n = self.financial_assets.len();
        let mu: Arc<[f64]> = Arc::from(
            self.financial_assets
                .iter()
                .map(|x| x.annualized_profit_rate)
                .collect::<Vec<_>>(),
        );
        let sigma = self.get_asset_covariance_matrix()?;
        let penalty = 1e3; // 收益偏离惩罚系数
        let mu_closure = mu.clone();
        let sigma_closure = sigma.clone();
        let l2_value = self.l2;
        let f = move |w: &[f64], cost: &mut f64| -> Result<(), SolverError> {
            let var = w
                .iter()
                .enumerate()
                .map(|(i, &wi)| {
                    w.iter()
                        .enumerate()
                        .map(|(j, &wj)| wi * wj * sigma_closure[i][j])
                        .sum::<f64>()
                })
                .sum::<f64>();
            let mean = mu_closure
                .iter()
                .zip(w)
                .map(|(&m, &wi)| m * wi)
                .sum::<f64>();
            *cost = var
                + penalty * (mean - target_return).powi(2)
                + l2_value * w.iter().map(|wi| wi.powi(2)).sum::<f64>();
            Ok(())
        };

        let df = move |w: &[f64], grad: &mut [f64]| -> Result<(), SolverError> {
            let mean = mu.iter().zip(w).map(|(&m, &wi)| m * wi).sum::<f64>();
            for i in 0..n {
                let grad_var_i = 2.0 * sigma[i].iter().zip(w).map(|(&s, &wj)| s * wj).sum::<f64>();
                grad[i] = grad_var_i + 2.0 * penalty * (mean - target_return) * mu[i];
            }
            Ok(())
        };

        let constraints = Simplex::new(1.0);
        let problem = Problem::new(&constraints, df, f);
        let mut cache = PANOCCache::new(n, 1e-12, 10);
        let mut panoc = PANOCOptimizer::new(problem, &mut cache).with_max_iter(max_iter);

        let mut w = vec![1.0 / n as f64; n];
        panoc
            .solve(&mut w)
            .map_err(|e| anyhow!(format!("{e:#?}")))?;

        for (asset, &wi) in self.financial_assets.iter_mut().zip(&w) {
            asset.weight = wi;
        }

        Ok(())
    }

    pub fn optimize_risk_parity(&mut self, max_iter: usize) -> anyhow::Result<()> {
        let n = self.financial_assets.len();
        let sigma = self.get_asset_covariance_matrix()?;

        let f = |w: &[f64], cost: &mut f64| -> Result<(), SolverError> {
            let sigma_w: Vec<f64> = (0..n)
                .map(|i| sigma[i].iter().zip(w).map(|(&s, &wj)| s * wj).sum::<f64>())
                .collect();
            let port_var = w
                .iter()
                .zip(&sigma_w)
                .map(|(&wi, &sw)| wi * sw)
                .sum::<f64>();
            let port_std = port_var.sqrt();

            // 风险贡献差异越小越好
            let rc: Vec<f64> = w
                .iter()
                .zip(&sigma_w)
                .map(|(&wi, &sw)| wi * sw / port_std)
                .collect();
            let mean_rc = rc.iter().sum::<f64>() / n as f64;
            *cost = rc.iter().map(|r| (r - mean_rc).powi(2)).sum::<f64>();
            Ok(())
        };

        let df = |_: &[f64], _: &mut [f64]| Ok(()); // 可用有限差分近似梯度

        let constraints = Simplex::new(1.0);
        let problem = Problem::new(&constraints, df, f);
        let mut cache = PANOCCache::new(n, 1e-12, 10);
        let mut panoc = PANOCOptimizer::new(problem, &mut cache).with_max_iter(max_iter);

        let mut w = vec![1.0 / n as f64; n];
        panoc
            .solve(&mut w)
            .map_err(|e| anyhow!(format!("{e:#?}")))?;

        for (asset, &wi) in self.financial_assets.iter_mut().zip(&w) {
            asset.weight = wi;
        }
        Ok(())
    }

    pub async fn add_stock_from_codes(
        &mut self,
        codes: &[Arc<str>],
        window_days: u64,
    ) -> anyhow::Result<()> {
        let mut handles = Vec::with_capacity(codes.len());

        for code in codes.iter() {
            let moved_code = code.clone();
            handles.push(async move {
                (
                    moved_code.clone(),
                    get_adjusted_share_price(&moved_code, AdjustDirection::Forward).await,
                )
            });
        }
        let now = Local::now().date_naive();
        let days = Days::new(window_days);
        let start_date = now - days;
        for handle in handles {
            let (code, prices) = handle.await;
            let prices = prices?
                .into_iter()
                .filter(|x| x.date() >= &start_date)
                .copied()
                .collect::<Arc<[AssetPrice]>>();
            self.add_stock(code, prices)?;
        }
        Ok(())
    }

    pub fn get_asset(&self, code: impl AsRef<str>) -> Option<&FinancialAsset> {
        let code = code.as_ref();
        for asset in self.financial_assets.iter() {
            if asset.code().as_ref() == code {
                return Some(asset);
            }
        }
        None
    }

    pub fn max_return_rate(&self) -> Option<f64> {
        self.financial_assets
            .iter()
            .map(|x| x.annualized_profit_rate)
            .max_by(|a, b| a.total_cmp(b))
    }

    pub fn min_return_rate(&self) -> Option<f64> {
        self.financial_assets
            .iter()
            .map(|x| x.annualized_profit_rate)
            .min_by(|a, b| a.total_cmp(b))
    }

    pub fn max_standard_deviation(&mut self) -> Option<f64> {
        for asset in self.financial_assets.iter_mut() {
            asset.calculate_std(&self.intersected_dates);
        }

        self.financial_assets
            .iter()
            .map(|x| *x.std())
            .max_by(|a, b| a.total_cmp(b))
    }

    pub fn min_standard_deviation(&mut self) -> Option<f64> {
        for asset in self.financial_assets.iter_mut() {
            asset.calculate_std(&self.intersected_dates);
        }

        self.financial_assets
            .iter()
            .map(|x| *x.std())
            .min_by(|a, b| a.total_cmp(b))
    }

    pub async fn efficient_frontier(
        codes: &[Arc<str>],
        window_days: u64,
        n_dots: usize,
        max_iter: usize,
        l2: f64,
        covariance_lambda: f64,
        profit_type: ProfitRateCalculationType,
    ) -> anyhow::Result<Vec<EfficientFrontierDot>> {
        if n_dots < 2 {
            bail!("n_dots must be >= 2")
        }

        let mut portfolio_prototype = Self::new();
        portfolio_prototype
            .add_stock_from_codes(&codes, window_days)
            .await?;

        portfolio_prototype.set_covariance_lambda(covariance_lambda);
        portfolio_prototype.set_l2(l2);
        portfolio_prototype.set_profit_type(profit_type);
        portfolio_prototype.get_asset_covariance_matrix()?;

        let max_return = portfolio_prototype
            .max_return_rate()
            .context("failed to get maximum return of the portfolio")?;
        let min_return = portfolio_prototype
            .min_return_rate()
            .context("failed to get minimum return of the portfolio")?;

        let step = (max_return - min_return) / ((n_dots - 1) as f64);
        let result = (0..n_dots)
            .into_par_iter()
            .map(|i| {
                let mut portfolio = portfolio_prototype.clone(); // 每个线程独立 clone
                let target_return = min_return + step * (i as f64);
                portfolio.optimize_min_variance_given_return(target_return, max_iter)?;
                Ok(EfficientFrontierDot {
                    standard_deviation: portfolio.standard_deviation()?,
                    return_rate: portfolio.expected_return(),
                })
            })
            .collect::<anyhow::Result<Vec<_>>>()?;
        Ok(result)
    }
}

#[tokio::test]
async fn main() -> anyhow::Result<()> {
    use crate::data_source::risk_free_rate::get_risk_free_rate;
    use hashbrown::HashMap;
    std::env::set_var("RUST_BACKTRACE", "1");
    let codes = [
        "002351", "002670", "002104", "002017", "601138", "002456", "002436", "002049", "603986",
        "300062", "601288",
    ];
    let codes = codes
        .iter()
        .map(|x| Arc::from(*x))
        .collect::<Vec<Arc<str>>>();
    let rf = get_risk_free_rate().await.unwrap();
    let mut porfolio = Portfolio::new();
    porfolio.add_stock_from_codes(&codes, 1500).await.unwrap();
    porfolio.optimize_max_sharpe(rf, 10_000)?;
    let widgets = porfolio.get_asset_weights();
    let map: HashMap<Arc<str>, f64> = HashMap::from_iter(widgets);

    println!("portfolio weights: {:#?}", map);
    println!("portfolio expected return: {}", porfolio.expected_return());
    println!(
        "portfolio standard deviation: {}",
        porfolio.standard_deviation().unwrap()
    );
    let corr_matrix = porfolio.get_correlation_coefficient_matrix()?;
    let n = codes.len();
    println!("相关系数：");
    for i in 0..n {
        for j in 0..n {
            println!("{}-{}: {}", codes[i], codes[j], corr_matrix[i][j])
        }
    }

    println!("标准差:");
    for i in 0..n {
        println!(
            "{}: {}",
            codes[i],
            porfolio.get_asset(&codes[i]).unwrap().std()
        );
    }

    println!("平均年化：");
    for i in 0..n {
        println!(
            "{}: {}",
            codes[i],
            porfolio
                .get_asset(&codes[i])
                .unwrap()
                .annualized_profit_rate()
        );
    }

    Ok(())
}
