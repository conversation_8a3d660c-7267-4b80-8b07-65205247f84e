<template>
    <div class="settings-test">
        <h3>Settings Test Component</h3>
        
        <div class="test-section">
            <h4>Current Settings:</h4>
            <pre>{{ JSON.stringify(settingsStore.settings, null, 2) }}</pre>
        </div>
        
        <div class="test-section">
            <h4>Current Theme:</h4>
            <p>Theme: {{ settingsStore.settings.theme }}</p>
            <p>Naive Theme: {{ settingsStore.naiveTheme?.name || 'undefined' }}</p>
        </div>
        
        <div class="test-section">
            <h4>Current Language:</h4>
            <p>Settings Language: {{ settingsStore.settings.language }}</p>
            <p>I18n Language: {{ i18nStore.currentLanguage }}</p>
            <p>Sample Translation: {{ i18nStore.t.settings.title }}</p>
        </div>
        
        <div class="test-section">
            <h4>Sidebar State:</h4>
            <p>Sidebar Expanded: {{ settingsStore.settings.sidebarExpanded }}</p>
        </div>
        
        <div class="test-section">
            <h4>Test Actions:</h4>
            <NButton @click="testThemeSwitch" type="primary" style="margin-right: 8px;">
                Switch Theme
            </NButton>
            <NButton @click="testLanguageSwitch" type="primary" style="margin-right: 8px;">
                Switch Language
            </NButton>
            <NButton @click="testSidebarToggle" type="primary">
                Toggle Sidebar
            </NButton>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { NButton } from 'naive-ui';
import { useSettingsStore } from '../stores/settings';
import { useI18nStore } from '../stores/i18n';

const settingsStore = useSettingsStore();
const i18nStore = useI18nStore();

const testThemeSwitch = () => {
    const themes = ['dark', 'light', 'auto'] as const;
    const currentIndex = themes.indexOf(settingsStore.settings.theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    settingsStore.updateSettings({ theme: themes[nextIndex] });
};

const testLanguageSwitch = () => {
    const newLanguage = settingsStore.settings.language === 'zh-CN' ? 'en-US' : 'zh-CN';
    settingsStore.updateSettings({ language: newLanguage });
};

const testSidebarToggle = () => {
    settingsStore.updateSettings({ sidebarExpanded: !settingsStore.settings.sidebarExpanded });
};
</script>

<style scoped>
.settings-test {
    padding: 20px;
    max-width: 800px;
}

.test-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid var(--n-border-color);
    border-radius: 6px;
}

.test-section h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--n-text-color);
}

.test-section pre {
    background: var(--n-code-color);
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
}

.test-section p {
    margin: 5px 0;
    color: var(--n-text-color);
}
</style>
