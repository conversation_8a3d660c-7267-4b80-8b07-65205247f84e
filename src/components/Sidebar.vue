<template>
    <div class="sidebar-container" :class="{ collapsed: isCollapsed }">
        <!-- 侧边栏头部 -->
        <div class="sidebar-header">
            <div class="logo-section">
                <NIcon size="28" class="logo-icon">
                    <TrendingUp />
                </NIcon>
                <span v-if="!isCollapsed" class="logo-text">量化投资</span>
            </div>
            <NButton
                quaternary
                circle
                size="small"
                @click="toggleSidebar"
                class="collapse-btn"
            >
                <NIcon>
                    <ChevronBackOutline v-if="!isCollapsed" />
                    <ChevronForwardOutline v-else />
                </NIcon>
            </NButton>
        </div>

        <!-- 导航菜单 -->
        <div class="sidebar-content">
            <NMenu
                :value="activeKey"
                :options="menuOptions"
                :collapsed="isCollapsed"
                :collapsed-width="64"
                :collapsed-icon-size="22"
                @update:value="handleMenuSelect"
                class="sidebar-menu"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, h } from 'vue';
import { NIcon, NMenu, NButton } from 'naive-ui';
import {
    Search,
    TrendingUp,
    ChevronBackOutline,
    ChevronForwardOutline,
    PieChart,
    List,
    Settings
} from '@vicons/ionicons5';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

// 侧边栏状态
const isCollapsed = ref(false);

// 当前激活的菜单项
const activeKey = computed(() => {
    const path = route.path;
    if (path === '/') return 'search';
    if (path.startsWith('/portfolio')) return 'portfolio';
    if (path.startsWith('/stocks')) return 'stocks';
    if (path.startsWith('/settings')) return 'settings';
    return 'search';
});

// 菜单选项
const menuOptions = computed(() => [
    {
        label: '股票搜索',
        key: 'search',
        icon: () => h(NIcon, null, { default: () => h(Search) })
    },
    {
        label: '投资组合',
        key: 'portfolio',
        icon: () => h(NIcon, null, { default: () => h(PieChart) })
    },
    {
        label: '股票市场',
        key: 'stocks',
        icon: () => h(NIcon, null, { default: () => h(List) })
    },
    {
        type: 'divider',
        key: 'divider'
    },
    {
        label: '设置',
        key: 'settings',
        icon: () => h(NIcon, null, { default: () => h(Settings) })
    }
]);

// 切换侧边栏折叠状态
const toggleSidebar = () => {
    isCollapsed.value = !isCollapsed.value;
};

// 处理菜单选择
const handleMenuSelect = (key: string) => {
    switch (key) {
        case 'search':
            router.push('/');
            break;
        case 'portfolio':
            router.push('/portfolio');
            break;
        case 'stocks':
            router.push('/stocks');
            break;
        case 'settings':
            router.push('/settings');
            break;
    }
};
</script>

<style scoped>
.sidebar-container {
    height: 100vh;
    width: 280px;
    background: var(--n-color);
    border-right: 1px solid var(--n-border-color);
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;
    position: relative;
}

.sidebar-container.collapsed {
    width: 64px;
}

.sidebar-header {
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--n-border-color);
    min-height: 64px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    color: #63e2b7;
    flex-shrink: 0;
}

.logo-text {
    font-size: 18px;
    font-weight: 600;
    color: var(--n-text-color);
    white-space: nowrap;
}

.collapse-btn {
    flex-shrink: 0;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.sidebar-menu {
    border: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar-container {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 1000;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    }
    
    .sidebar-container.collapsed {
        transform: translateX(-100%);
        width: 280px;
    }
}
</style>
