<template>
    <div class="portfolio-page">
        <div class="page-header">
            <div class="header-content">
                <h2 class="page-title">投资组合管理</h2>
                <p class="page-description">
                    创建和优化您的投资组合，基于现代投资组合理论
                </p>
            </div>
            <div class="header-buttons">
                <NButton
                    type="primary"
                    size="large"
                    @click="showCreateModal = true"
                >
                    <template #icon>
                        <NIcon>
                            <Add />
                        </NIcon>
                    </template>
                    新建组合
                </NButton>
            </div>
        </div>

        <!-- 投资组合列表 -->
        <div class="portfolio-list">
            <div v-if="portfolios.length === 0" class="empty-state">
                <NIcon size="64" class="empty-icon">
                    <PieChart />
                </NIcon>
                <h3>暂无投资组合</h3>
                <p>创建您的第一个投资组合，开始量化投资之旅</p>
                <NButton type="primary" @click="showCreateModal = true">
                    立即创建
                </NButton>
            </div>

            <div v-else class="portfolio-grid">
                <NCard
                    v-for="portfolio in portfolios"
                    :key="portfolio.id"
                    class="portfolio-card"
                    hoverable
                >
                    <template #header>
                        <div class="portfolio-header">
                            <h3>{{ portfolio.name }}</h3>
                            <NDropdown
                                :options="getPortfolioActions(portfolio)"
                                @select="
                                    (key: string) =>
                                        handlePortfolioAction(key, portfolio)
                                "
                            >
                                <NButton quaternary circle size="small">
                                    <NIcon>
                                        <EllipsisVertical />
                                    </NIcon>
                                </NButton>
                            </NDropdown>
                        </div>
                    </template>

                    <div class="portfolio-content">
                        <div class="portfolio-stats">
                            <div class="stat-item">
                                <span class="stat-label">股票数量</span>
                                <span class="stat-value">{{
                                    portfolio.stocks.length
                                }}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">预期收益</span>
                                <span
                                    class="stat-value"
                                    :class="
                                        getReturnClass(portfolio.expectedReturn)
                                    "
                                >
                                    {{
                                        formatPercent(portfolio.expectedReturn)
                                    }}
                                </span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">风险水平</span>
                                <span class="stat-value">{{
                                    formatPercent(portfolio.risk)
                                }}</span>
                            </div>
                        </div>

                        <div class="portfolio-stocks">
                            <div class="stock-tags">
                                <NTag
                                    v-for="stock in portfolio.stocks.slice(
                                        0,
                                        3,
                                    )"
                                    :key="stock"
                                    size="small"
                                    class="stock-tag"
                                >
                                    {{ stock }}
                                </NTag>
                                <span
                                    v-if="portfolio.stocks.length > 3"
                                    class="more-stocks"
                                >
                                    +{{ portfolio.stocks.length - 3 }}
                                </span>
                            </div>
                        </div>

                        <div class="portfolio-actions">
                            <NButton
                                size="small"
                                @click="optimizePortfolio(portfolio)"
                                :loading="portfolio.optimizing"
                            >
                                优化组合
                            </NButton>
                            <NButton
                                size="small"
                                quaternary
                                @click="viewPortfolioDetails(portfolio)"
                            >
                                查看详情
                            </NButton>
                        </div>
                    </div>
                </NCard>
            </div>
        </div>

        <!-- 创建投资组合模态框 -->
        <NModal
            v-model:show="showCreateModal"
            preset="card"
            title="创建投资组合"
            style="width: 80vw"
        >
            <CreatePortfolioForm
                @success="handleCreateSuccess"
                @cancel="showCreateModal = false"
            />
        </NModal>

        <!-- 编辑投资组合模态框 -->
        <NModal
            v-model:show="showEditModal"
            preset="card"
            title="编辑投资组合"
            style="width: 80vw"
        >
            <CreatePortfolioForm
                v-if="editingPortfolio"
                :initial-data="editingPortfolio"
                @success="handleEditSuccess"
                @cancel="
                    showEditModal = false;
                    editingPortfolio = null;
                "
            />
        </NModal>

        <!-- 优化结果模态框 -->
        <NModal
            v-model:show="showOptimizeModal"
            preset="card"
            title="优化结果"
            style="width: 80vw"
        >
            <OptimizationResult
                v-if="optimizationResult"
                :result="optimizationResult"
                @close="showOptimizeModal = false"
            />
        </NModal>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import {
    NButton,
    NIcon,
    NCard,
    NTag,
    NDropdown,
    NModal,
    useMessage,
    useDialog,
} from "naive-ui";
import { Add, PieChart, EllipsisVertical } from "@vicons/ionicons5";
import CreatePortfolioForm from "../components/CreatePortfolioForm.vue";
import OptimizationResult from "../components/OptimizationResult.vue";
import { invoke } from "@tauri-apps/api/core";

const message = useMessage();
const dialog = useDialog();

// 优化结果类型
interface OptimizationResult {
    portfolioName: string;
    optimizationType: string;
    expectedReturn: number;
    risk: number;
    weights: Array<{
        code: string;
        weight: number;
    }>;
    // 添加用于生成有效前沿的参数
    stocks?: string[];
    windowDays?: number;
    maxIterations?: number;
    l2?: number;
    covariance_lambda?: number;
    profitType?: string;
}

// API结果类型
interface ApiOptimizationResult {
    expected_return: number;
    standard_deviation: number;
    optimization_type: string;
    asset_weights: Array<{
        code: string;
        weight: number;
    }>;
}

// 页面状态
const showCreateModal = ref(false);
const showOptimizeModal = ref(false);
const showEditModal = ref(false);
const optimizationResult = ref<OptimizationResult | null>(null);
const editingPortfolio = ref<Portfolio | null>(null);

// 投资组合数据类型
interface Portfolio {
    id: string;
    name: string;
    description?: string;
    stocks: string[];
    expectedReturn: number;
    risk: number;
    optimizing?: boolean;
    createdAt: Date;
    // 优化参数
    optimizationGoal?: string;
    targetReturn?: number;
    targetVariance?: number;
    windowDays?: number;
    maxIterations?: number;
    profitType?: string;
    l2?: number;
    covariance_lambda: number;
}

// 投资组合列表
const portfolios = ref<Portfolio[]>([
    {
        id: "1",
        name: "示例投资组合",
        description: "这是一个基于现代投资组合理论的示例组合，包含三只优质股票",
        stocks: ["002351", "002670", "002104"],
        optimizationGoal: "min_variance",
        targetReturn: 0.12,
        targetVariance: 0.18,
        expectedReturn: 0,
        risk: 0,
        createdAt: new Date(),
        optimizing: false,
        windowDays: 365,
        maxIterations: 1000,
        profitType: "Logarithm",
        l2: 0.0,
        covariance_lambda: 0.0,
    },
    {
        id: "2",
        name: "高收益组合",
        description: "追求高收益的投资组合，适合风险承受能力较强的投资者",
        stocks: ["002017", "601138", "002456"],
        optimizationGoal: "max_return_given_variance",
        targetReturn: 0.15,
        targetVariance: 0.25,
        expectedReturn: 0,
        risk: 0,
        createdAt: new Date(Date.now() - 86400000), // 昨天创建
        optimizing: false,
        windowDays: 252,
        maxIterations: 2000,
        profitType: "Arithmetic",
        l2: 0.0,
        covariance_lambda: 0.0,
    },
]);

// 格式化百分比
const formatPercent = (value: number) => {
    return (value * 100).toFixed(2) + "%";
};

// 获取收益率颜色类
const getReturnClass = (value: number) => {
    if (value > 0.05) return "positive";
    if (value < 0) return "negative";
    return "neutral";
};

// 获取投资组合操作菜单
const getPortfolioActions = (_portfolio: Portfolio) => [
    {
        label: "编辑",
        key: "edit",
    },
    {
        label: "复制",
        key: "copy",
    },
    {
        label: "删除",
        key: "delete",
    },
];

// 处理投资组合操作
const handlePortfolioAction = (key: string, portfolio: Portfolio) => {
    switch (key) {
        case "edit":
            editPortfolio(portfolio);
            break;
        case "copy":
            copyPortfolio(portfolio);
            break;
        case "delete":
            deletePortfolio(portfolio);
            break;
    }
};

// 优化投资组合
const optimizePortfolio = async (portfolio: Portfolio) => {
    portfolio.optimizing = true;

    try {
        let result: ApiOptimizationResult;

        // 根据优化目标调用不同的API
        switch (portfolio.optimizationGoal) {
            case "min_variance":
                console.log("调用 optimize_min_variance API，参数:", {
                    stock_codes: portfolio.stocks,
                    window_days: portfolio.windowDays || 365,
                    max_iter: portfolio.maxIterations || 1000,
                    profit_type: portfolio.profitType || "Logarithm",
                });
                result = (await invoke("optimize_min_variance", {
                    request: {
                        stock_codes: portfolio.stocks,
                        window_days: portfolio.windowDays || 365,
                        max_iter: portfolio.maxIterations || 1000,
                        profit_type: portfolio.profitType || "Logarithm",
                        l2: portfolio.l2 || 0.0,
                    },
                })) as ApiOptimizationResult;
                console.log("API返回结果:", result);
                break;

            case "min_variance_given_return":
                result = (await invoke("optimize_min_variance_given_return", {
                    request: {
                        stock_codes: portfolio.stocks,
                        window_days: portfolio.windowDays || 365,
                        target_return: portfolio.targetReturn || 0.08,
                        max_iter: portfolio.maxIterations || 1000,
                        profit_type: portfolio.profitType || "Logarithm",
                        l2: portfolio.l2 || 0.0,
                        covariance_lambda: portfolio.covariance_lambda || 0.0,
                    },
                })) as ApiOptimizationResult;
                break;

            case "max_return_given_variance":
                result = (await invoke("optimize_max_return_given_variance", {
                    request: {
                        stock_codes: portfolio.stocks,
                        window_days: portfolio.windowDays || 365,
                        target_variance: portfolio.targetVariance || 0.15,
                        max_iter: portfolio.maxIterations || 1000,
                        profit_type: portfolio.profitType || "Logarithm",
                        l2: portfolio.l2 || 0.0,
                        covariance_lambda: portfolio.covariance_lambda || 0.0,
                    },
                })) as ApiOptimizationResult;
                break;
            case "max_sharp_ratio":
                result = (await invoke("optimize_max_sharp_ratio", {
                    request: {
                        stock_codes: portfolio.stocks,
                        window_days: portfolio.windowDays || 365,
                        target_variance: portfolio.targetVariance || 0.15,
                        max_iter: portfolio.maxIterations || 1000,
                        profit_type: portfolio.profitType || "Logarithm",
                        l2: portfolio.l2 || 0.0,
                        covariance_lambda: portfolio.covariance_lambda || 0.0,
                    },
                })) as ApiOptimizationResult;
                break;
            case "risk_parity":
                result = (await invoke("optimize_risk_parity", {
                    request: {
                        stock_codes: portfolio.stocks,
                        window_days: portfolio.windowDays || 365,
                        target_variance: portfolio.targetVariance || 0.15,
                        max_iter: portfolio.maxIterations || 1000,
                        profit_type: portfolio.profitType || "Logarithm",
                        l2: portfolio.l2 || 0.0,
                        covariance_lambda: portfolio.covariance_lambda || 0.0,
                    },
                })) as ApiOptimizationResult;
                break;
            default:
                throw new Error("未知的优化目标");
        }

        // 更新投资组合的预期收益和风险
        portfolio.expectedReturn = result.expected_return;
        portfolio.risk = result.standard_deviation;

        // 设置优化结果
        optimizationResult.value = {
            portfolioName: portfolio.name,
            optimizationType: result.optimization_type,
            expectedReturn: result.expected_return,
            risk: result.standard_deviation,
            weights: result.asset_weights,
            // 添加生成有效前沿所需的参数
            stocks: portfolio.stocks,
            windowDays: portfolio.windowDays || 365,
            maxIterations: portfolio.maxIterations || 1000,
            l2: portfolio.l2 || 0.0,
            covariance_lambda: portfolio.covariance_lambda || 0.0,
            profitType: portfolio.profitType || "Logarithm",
        };

        showOptimizeModal.value = true;
        message.success("投资组合优化完成");
    } catch (error) {
        message.error("优化失败: " + (error as string));
        console.error("Optimization failed:", error);
    } finally {
        portfolio.optimizing = false;
    }
};

// 查看投资组合详情
const viewPortfolioDetails = (_portfolio: Portfolio) => {
    message.info("详情页面正在开发中...");
};

// 编辑投资组合
const editPortfolio = (portfolio: Portfolio) => {
    editingPortfolio.value = { ...portfolio };
    showEditModal.value = true;
};

// 复制投资组合
const copyPortfolio = (portfolio: Portfolio) => {
    const newPortfolio: Portfolio = {
        ...portfolio,
        id: Date.now().toString(),
        name: portfolio.name + " (副本)",
        createdAt: new Date(),
    };
    portfolios.value.push(newPortfolio);
    message.success("投资组合已复制");
};

// 删除投资组合
const deletePortfolio = (portfolio: Portfolio) => {
    dialog.warning({
        title: "确认删除",
        content: `确定要删除投资组合"${portfolio.name}"吗？此操作不可撤销。`,
        positiveText: "删除",
        negativeText: "取消",
        onPositiveClick: () => {
            portfolios.value = portfolios.value.filter(
                (p) => p.id !== portfolio.id,
            );
            message.success("投资组合已删除");
        },
    });
};

// 处理创建成功
const handleCreateSuccess = (newPortfolio: Portfolio) => {
    portfolios.value.push(newPortfolio);
    showCreateModal.value = false;
    message.success("投资组合创建成功");
};

// 处理编辑成功
const handleEditSuccess = (updatedPortfolio: Portfolio) => {
    const index = portfolios.value.findIndex(
        (p) => p.id === updatedPortfolio.id,
    );
    if (index !== -1) {
        portfolios.value[index] = updatedPortfolio;
        showEditModal.value = false;
        editingPortfolio.value = null;
        message.success("投资组合更新成功");
    }
};

// 初始化数据
onMounted(() => {
    // 加载保存的投资组合
    loadPortfolios();
});

// 加载投资组合数据
const loadPortfolios = () => {
    const saved = localStorage.getItem("portfolios");
    if (saved) {
        portfolios.value = JSON.parse(saved).map((p: any) => ({
            ...p,
            createdAt: new Date(p.createdAt),
        }));
    }
};

// 保存投资组合数据
const savePortfolios = () => {
    localStorage.setItem("portfolios", JSON.stringify(portfolios.value));
};

// 监听投资组合变化并保存
import { watch } from "vue";
watch(portfolios, savePortfolios, { deep: true });
</script>

<style scoped>
.portfolio-page {
    padding: 24px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
}

.header-content h2 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
}

.header-content p {
    margin: 0;
    color: var(--n-text-color-2);
    font-size: 16px;
}

.header-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.empty-state {
    text-align: center;
    padding: 64px 24px;
}

.empty-icon {
    color: var(--n-text-color-3);
    margin-bottom: 16px;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
}

.empty-state p {
    margin: 0 0 24px 0;
    color: var(--n-text-color-2);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
}

.portfolio-card {
    transition: all 0.2s ease;
}

.portfolio-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.portfolio-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.portfolio-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.portfolio-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: var(--n-text-color-2);
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
}

.stat-value.positive {
    color: #2ecc71;
}

.stat-value.negative {
    color: #e74c3c;
}

.stat-value.neutral {
    color: var(--n-text-color);
}

.stock-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.stock-tag {
    font-size: 12px;
}

.more-stocks {
    font-size: 12px;
    color: var(--n-text-color-2);
}

.portfolio-actions {
    display: flex;
    gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .portfolio-page {
        padding: 16px;
    }

    .page-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .portfolio-stats {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}
</style>
