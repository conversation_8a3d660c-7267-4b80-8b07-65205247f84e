<template>
    <NConfigProvider :theme="settingsStore.naiveTheme">
        <NGlobalStyle />
        <NDialogProvider>
            <NMessageProvider>
                <NLoadingBarProvider>
                    <div class="app-layout">
                        <!-- 侧边栏 -->
                        <Sidebar />

                        <!-- 主内容区域 -->
                        <div class="main-content">
                            <!-- 页面内容 -->
                            <div class="page-content">
                                <ErrorBoundary>
                                    <RouterView />
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>

                    <!-- 全局加载组件 -->
                    <GlobalLoading
                        :is-loading="globalLoading.isLoading"
                        :loading-text="globalLoading.text"
                        :progress="globalLoading.progress"
                    />
                </NLoadingBarProvider>
            </NMessageProvider>
        </NDialogProvider>
    </NConfigProvider>
</template>

<script lang="ts" setup>
import {
    NMessageProvider,
    NDialogProvider,
    NLoadingBarProvider,
    NConfigProvider,
    NGlobalStyle,
} from "naive-ui";
import Sidebar from "./components/Sidebar.vue";
import ErrorBoundary from "./components/ErrorBoundary.vue";
import GlobalLoading from "./components/GlobalLoading.vue";
import { ref, onMounted } from "vue";
import { useSettingsStore } from "./stores/settings";
import { useI18nStore } from "./stores/i18n";

const settingsStore = useSettingsStore();
const i18nStore = useI18nStore();

// 全局加载状态
const globalLoading = ref({
    isLoading: false,
    text: "加载中...",
    progress: 0,
});

// 初始化设置
onMounted(() => {
    settingsStore.loadSettings();
    i18nStore.initializeFromSettings();
});
</script>

<style scoped>
.app-layout {
    display: flex;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; /* 防止flex子项溢出 */
}

.toolbar-container {
    flex-shrink: 0;
    border-bottom: 1px solid var(--n-border-color);
}

.page-content {
    flex: 1;
    overflow-y: auto;
    position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-layout {
        flex-direction: column;
    }
}
</style>
