import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { darkTheme, lightTheme } from 'naive-ui';
import { useI18nStore, type SupportedLanguage } from './i18n';

export interface AppSettings {
    theme: 'dark' | 'light' | 'auto';
    language: SupportedLanguage;
    sidebarExpanded: boolean;
    autoSave: boolean;
    cacheTime: number;
    defaultWindowDays: number;
    defaultMaxIterations: number;
    defaultProfitType: string;
    riskFreeRate: number;
}

const defaultSettings: AppSettings = {
    theme: 'dark',
    language: 'zh-CN',
    sidebarExpanded: true,
    autoSave: true,
    cacheTime: 30,
    defaultWindowDays: 365,
    defaultMaxIterations: 1000,
    defaultProfitType: 'Logarithm',
    riskFreeRate: 0.03
};

export const useSettingsStore = defineStore('settings', () => {
    // 设置状态
    const settings = ref<AppSettings>({ ...defaultSettings });

    // 获取 i18n store
    const i18nStore = useI18nStore();

    // 计算属性
    const naiveTheme = computed(() => {
        if (settings.value.theme === 'auto') {
            // 检测系统主题
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? darkTheme : lightTheme;
        }
        return settings.value.theme === 'dark' ? darkTheme : lightTheme;
    });

    // 加载设置
    const loadSettings = () => {
        try {
            const saved = localStorage.getItem('app-settings');
            if (saved) {
                const parsedSettings = JSON.parse(saved);
                settings.value = { ...defaultSettings, ...parsedSettings };
                // 同步语言设置到 i18n store
                i18nStore.setLanguage(settings.value.language);
            } else {
                // 如果没有保存的设置，使用默认语言
                i18nStore.setLanguage(settings.value.language);
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
            settings.value = { ...defaultSettings };
            i18nStore.setLanguage(settings.value.language);
        }
    };

    // 保存设置
    const saveSettings = () => {
        try {
            localStorage.setItem('app-settings', JSON.stringify(settings.value));
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    };

    // 更新设置
    const updateSettings = (newSettings: Partial<AppSettings>) => {
        const oldLanguage = settings.value.language;
        settings.value = { ...settings.value, ...newSettings };

        // 如果语言发生变化，同步到 i18n store
        if (newSettings.language && newSettings.language !== oldLanguage) {
            i18nStore.setLanguage(newSettings.language);
        }

        if (settings.value.autoSave) {
            saveSettings();
        }
    };

    // 重置设置
    const resetSettings = () => {
        settings.value = { ...defaultSettings };
        saveSettings();
    };

    // 清除所有数据
    const clearAllData = () => {
        localStorage.clear();
        settings.value = { ...defaultSettings };
    };

    // 监听设置变化，自动保存
    watch(
        settings,
        () => {
            if (settings.value.autoSave) {
                saveSettings();
            }
        },
        { deep: true }
    );

    // 监听系统主题变化
    if (typeof window !== 'undefined') {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', () => {
            // 如果设置为跟随系统，触发响应式更新
            if (settings.value.theme === 'auto') {
                // 触发计算属性重新计算
                settings.value = { ...settings.value };
            }
        });
    }

    return {
        settings,
        naiveTheme,
        loadSettings,
        saveSettings,
        updateSettings,
        resetSettings,
        clearAllData
    };
});
